/**
 * Workflow Results Page - Redirect to Unified Experience
 * This page now redirects to the unified workflow experience
 */

'use client';

import React, { useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

export default function WorkflowResultsPage() {
  const params = useParams();
  const router = useRouter();
  const executionId = params.id as string;

  useEffect(() => {
    // Redirect to unified experience with results step
    if (executionId) {
      const unifiedUrl = `/workflow/unified?step=results&executionId=${executionId}`;
      router.replace(unifiedUrl);
    } else {
      // Fallback to unified experience
      router.replace('/workflow/unified');
    }
  }, [executionId, router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to unified workflow experience...</p>
      </div>
    </div>
  );
}
