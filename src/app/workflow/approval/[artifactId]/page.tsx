/**
 * Artifact Approval Page
 * Standalone page for approving workflow artifacts
 */

'use client';

import { useParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import ArtifactApproval from '../../../../components/Workflow/ArtifactApproval';
import Toast from '../../../../components/UI/Toast';

export default function ArtifactApprovalPage() {
  const params = useParams();
  const router = useRouter();
  const artifactId = params.artifactId as string;
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
  } | null>(null);

  const handleApprovalComplete = async (artifactId: string, approved: boolean) => {
    try {
      setIsRedirecting(true);

      // Fetch artifact details to get execution ID
      const response = await fetch(`/api/artifact/${artifactId}`);
      if (response.ok) {
        const result = await response.json();
        const artifact = result.data || result;

        if (artifact.executionId) {
          // Show success message with toast
          const message = approved
            ? 'Artifact approved successfully! Redirecting to results...'
            : 'Artifact rejected. Redirecting to workflow...';

          setToast({
            message,
            type: approved ? 'success' : 'info'
          });

          // Add a small delay to show the toast before redirecting
          setTimeout(() => {
            // Redirect to results page for approved artifacts, or back to workflow for rejected
            if (approved) {
              router.push(`/workflow/unified?step=results&executionId=${artifact.executionId}`);
            } else {
              router.push(`/workflow/unified?step=review&executionId=${artifact.executionId}`);
            }
          }, 1500); // 1.5 second delay
        } else {
          console.warn('No execution ID found in artifact');
          setToast({
            message: approved ? 'Artifact approved successfully!' : 'Artifact rejected.',
            type: approved ? 'success' : 'info'
          });
        }
      } else {
        console.error('Failed to fetch artifact details');
        setToast({
          message: 'Failed to fetch artifact details',
          type: 'error'
        });
      }
    } catch (error) {
      console.error('Error handling approval completion:', error);
      setToast({
        message: 'Error processing approval',
        type: 'error'
      });
    } finally {
      setIsRedirecting(false);
    }
  };

  if (!artifactId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Invalid Artifact ID</h1>
          <p className="text-gray-600">Please check the URL and try again.</p>
        </div>
      </div>
    );
  }

  if (isRedirecting) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Processing Approval</h2>
          <p className="text-gray-600">Redirecting to workflow results...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <ArtifactApproval
        artifactId={artifactId}
        onApprovalComplete={handleApprovalComplete}
        currentUser="demo-user" // In a real app, this would come from authentication
      />

      {/* Toast Notification */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
}
