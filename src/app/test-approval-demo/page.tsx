/**
 * Test Approval Demo Page
 * Demonstrates the approval functionality with mock data when Redis is unavailable
 */

'use client';

import { useState } from 'react';
import InlineApproval from '../../components/Workflow/InlineApproval';
import Toast from '../../components/UI/Toast';

export default function TestApprovalDemo() {
  const [showInlineApproval, setShowInlineApproval] = useState(false);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
  } | null>(null);

  // Mock artifact data for testing
  const mockArtifactId = 'd3def3e8-4754-4343-b6e6-7ff9122bde72';
  
  const handleApprovalComplete = (artifactId: string, approved: boolean) => {
    setToast({
      message: approved 
        ? `✅ Artifact ${artifactId.slice(-8)} approved successfully!` 
        : `❌ Artifact ${artifactId.slice(-8)} rejected.`,
      type: approved ? 'success' : 'info'
    });
    
    setShowInlineApproval(false);
    
    // Simulate redirect after delay
    setTimeout(() => {
      setToast({
        message: 'Redirecting to results page...',
        type: 'info'
      });
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            🧪 Approval System Demo
          </h1>
          
          <div className="space-y-6">
            {/* Info Section */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h2 className="font-semibold text-blue-900 mb-2">Demo Information</h2>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• This page demonstrates the approval functionality</li>
                <li>• Works even when Redis is unavailable</li>
                <li>• Shows toast notifications and UI interactions</li>
                <li>• Mock artifact ID: <code className="bg-blue-100 px-1 rounded">{mockArtifactId}</code></li>
              </ul>
            </div>

            {/* Current Issue Section */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h2 className="font-semibold text-yellow-900 mb-2">⚠️ Current Issues</h2>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• Redis connection failed (Upstash instance not accessible)</li>
                <li>• Review ID being used as Artifact ID (different systems)</li>
                <li>• Approval page expects artifact data, but getting review data</li>
              </ul>
            </div>

            {/* Demo Buttons */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-900">Test Approval Interfaces</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Inline Approval Demo */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Inline Approval Modal</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Opens approval interface in a modal overlay
                  </p>
                  <button
                    onClick={() => setShowInlineApproval(true)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    🔍 Open Inline Approval
                  </button>
                </div>

                {/* Standalone Page Demo */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Standalone Approval Page</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Opens approval page in new tab (will show error due to Redis)
                  </p>
                  <a
                    href={`/workflow/approval/${mockArtifactId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
                  >
                    🔗 Open Standalone Page
                  </a>
                </div>
              </div>
            </div>

            {/* URLs Section */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h2 className="font-semibold text-gray-900 mb-2">📋 Relevant URLs</h2>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Current Review URL:</span>
                  <br />
                  <code className="text-blue-600">
                    /workflow/unified?step=review&reviewId={mockArtifactId}
                  </code>
                </div>
                <div>
                  <span className="font-medium">Approval Page URL:</span>
                  <br />
                  <code className="text-blue-600">
                    /workflow/approval/{mockArtifactId}
                  </code>
                </div>
                <div>
                  <span className="font-medium">This Demo Page:</span>
                  <br />
                  <code className="text-blue-600">
                    /test-approval-demo
                  </code>
                </div>
              </div>
            </div>

            {/* Solutions Section */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h2 className="font-semibold text-green-900 mb-2">✅ Solutions Implemented</h2>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Added Redis fallback to prevent crashes</li>
                <li>• Created inline approval modal for better UX</li>
                <li>• Added toast notifications instead of browser alerts</li>
                <li>• Enhanced approval page with auto-redirect</li>
                <li>• Added link from review interface to approval page</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Inline Approval Modal */}
      {showInlineApproval && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Demo Approval Interface</h2>
              <button
                onClick={() => setShowInlineApproval(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <p className="text-sm text-yellow-800">
                <strong>Note:</strong> This will show an error because the artifact doesn't exist in the system. 
                This is expected when Redis is unavailable.
              </p>
            </div>
            
            <InlineApproval
              artifactId={mockArtifactId}
              onApprovalComplete={handleApprovalComplete}
              onClose={() => setShowInlineApproval(false)}
            />
          </div>
        </div>
      )}

      {/* Toast Notification */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
}
