/**
 * Test endpoint to check execution state and debug the issue
 */

import { NextRequest, NextResponse } from 'next/server';
import { getStateStore } from '../../../../core/workflow/singleton';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('executionId') || 'ec218daf-ed74-4d0d-83f1-a62b8f239580';
    
    const stateStore = getStateStore();
    
    // Get raw state
    const state = await stateStore.get();
    
    // Try to get the specific execution
    const execution = await stateStore.getExecution(executionId);
    
    // Get all executions
    const allExecutions = await stateStore.getAllExecutions();
    
    return NextResponse.json({
      success: true,
      data: {
        targetExecutionId: executionId,
        executionFound: !!execution,
        execution: execution ? {
          id: execution.id,
          status: execution.status,
          workflowId: execution.workflowId,
          progress: execution.progress,
          stepResultsCount: Object.keys(execution.stepResults || {}).length
        } : null,
        state: {
          hasState: !!state,
          hasExecutions: !!(state?.executions),
          executionCount: state?.executions ? Object.keys(state.executions).length : 0,
          executionKeys: state?.executions ? Object.keys(state.executions) : [],
          targetInState: state?.executions ? executionId in state.executions : false
        },
        allExecutions: {
          count: allExecutions.length,
          executions: allExecutions.map(exec => ({
            id: exec.id,
            status: exec.status,
            workflowId: exec.workflowId,
            progress: exec.progress
          }))
        }
      }
    });
    
  } catch (error) {
    console.error('❌ Test execution state error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
