/**
 * Direct Redis State Inspector
 * Check what's actually stored in Redis
 */

import { NextRequest, NextResponse } from 'next/server';
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'inspect';
    
    if (action === 'inspect') {
      // Get the raw state from Redis
      const rawState = await redis.json.get('workflow:system_state');
      
      // Get all keys with workflow prefix
      const allKeys = await redis.keys('workflow:*');
      
      // Get some sample keys
      const keyData: Record<string, any> = {};
      for (const key of allKeys.slice(0, 10)) {
        try {
          keyData[key] = await redis.json.get(key);
        } catch (error) {
          keyData[key] = `Error: ${error}`;
        }
      }
      
      return NextResponse.json({
        success: true,
        data: {
          rawState: rawState ? {
            hasExecutions: !!(rawState as any)?.executions,
            executionCount: (rawState as any)?.executions ? Object.keys((rawState as any).executions).length : 0,
            executionKeys: (rawState as any)?.executions ? Object.keys((rawState as any).executions).slice(0, 10) : [],
            hasWorkflows: !!(rawState as any)?.workflows,
            workflowCount: (rawState as any)?.workflows ? Object.keys((rawState as any).workflows).length : 0,
            version: (rawState as any)?.version,
            lastUpdated: (rawState as any)?.lastUpdated
          } : null,
          allKeys,
          keyData,
          targetExecution: (rawState as any)?.executions?.['ec218daf-ed74-4d0d-83f1-a62b8f239580'] || null
        }
      });
    }
    
    if (action === 'recreate') {
      // Recreate the missing execution for testing
      const executionId = 'ec218daf-ed74-4d0d-83f1-a62b8f239580';
      
      // Get current state
      const currentState = await redis.json.get('workflow:system_state') as any;
      
      if (currentState) {
        // Add the missing execution
        const testExecution = {
          id: executionId,
          workflowId: 'e884072f-8e5d-4a34-b665-669d951fe18b',
          status: 'completed',
          inputs: {
            topic: 'AI in Healthcare',
            target_audience: 'Healthcare professionals',
            primary_keyword: 'AI healthcare solutions'
          },
          outputs: {},
          stepResults: {
            'topic-input': {
              stepId: 'topic-input',
              status: 'completed',
              startedAt: '2025-06-13T10:36:01.159Z',
              completedAt: '2025-06-13T10:36:05.159Z',
              inputs: {},
              outputs: {
                topic: 'AI in Healthcare',
                target_audience: 'Healthcare professionals',
                primary_keyword: 'AI healthcare solutions'
              }
            }
          },
          progress: 100,
          startedAt: '2025-06-13T10:36:01.159Z',
          completedAt: '2025-06-13T10:40:01.159Z',
          metadata: {
            source: 'debug-recreation',
            createdBy: 'debug-system'
          }
        };
        
        // Update state with the execution
        const updatedState = {
          ...currentState,
          executions: {
            ...currentState.executions,
            [executionId]: testExecution
          },
          lastUpdated: new Date().toISOString()
        };
        
        // Save back to Redis
        await redis.json.set('workflow:system_state', '$', updatedState);
        
        return NextResponse.json({
          success: true,
          message: `Recreated execution ${executionId}`,
          data: {
            executionId,
            executionCreated: true
          }
        });
      }
    }
    
    return NextResponse.json({
      success: false,
      error: 'Invalid action'
    }, { status: 400 });
    
  } catch (error) {
    console.error('❌ Redis state debug error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
