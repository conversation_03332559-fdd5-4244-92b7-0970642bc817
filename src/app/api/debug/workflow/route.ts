/**
 * Debug API for Workflow System
 * Helps diagnose issues with workflow execution and approval flow
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine, getStateStore } from '../../../../core/workflow/singleton';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('executionId');
    const action = searchParams.get('action') || 'status';

    const workflowEngine = getWorkflowEngine();
    const stateStore = getStateStore();

    if (action === 'executions') {
      // List all executions with detailed debugging
      const executions = await stateStore.getAllExecutions();
      const state = await stateStore.get();

      console.log('🔍 Debug executions - Raw state:', {
        hasState: !!state,
        hasExecutions: !!(state?.executions),
        executionKeys: state?.executions ? Object.keys(state.executions) : [],
        executionCount: executions.length,
        targetExecutionId: 'ec218daf-ed74-4d0d-83f1-a62b8f239580'
      });

      return NextResponse.json({
        success: true,
        data: {
          executions: executions.map(exec => ({
            id: exec.id,
            workflowId: exec.workflowId,
            status: exec.status,
            progress: exec.progress,
            currentStep: exec.currentStep,
            stepCount: Object.keys(exec.stepResults || {}).length,
            startedAt: exec.startedAt,
            completedAt: exec.completedAt
          })),
          debug: {
            stateExists: !!state,
            executionsExists: !!(state?.executions),
            executionKeys: state?.executions ? Object.keys(state.executions) : [],
            totalExecutions: executions.length,
            targetFound: state?.executions ? 'ec218daf-ed74-4d0d-83f1-a62b8f239580' in state.executions : false
          }
        }
      });
    }

    if (action === 'reviews') {
      // List all reviews
      const state = await stateStore.get();
      const reviews = state ? Object.values(state.reviews) : [];
      return NextResponse.json({
        success: true,
        data: { reviews }
      });
    }

    if (action === 'artifacts') {
      // List all artifacts
      const state = await stateStore.get();
      const artifacts = state ? Object.values(state.artifacts || {}) : [];
      return NextResponse.json({
        success: true,
        data: { artifacts }
      });
    }

    if (executionId) {
      // Get detailed execution info
      const execution = await workflowEngine.getExecution(executionId);
      if (!execution) {
        return NextResponse.json(
          { error: 'Execution not found' },
          { status: 404 }
        );
      }

      console.log(`🔍 Debug: Execution ${executionId} status: ${execution.status}`);

      const workflow = await workflowEngine.getWorkflow(execution.workflowId);
      
      // Get step details with types
      const stepDetails = Object.entries(execution.stepResults).map(([stepId, result]) => {
        const workflowStep = workflow?.steps.find(s => s.id === stepId);
        return {
          stepId,
          stepType: workflowStep?.type,
          stepName: workflowStep?.name,
          status: result.status,
          artifactId: result.artifactId,
          approvalRequired: result.approvalRequired,
          hasReviewConfig: !!workflowStep?.config.reviewConfig,
          outputs: Object.keys(result.outputs),
          error: result.error
        };
      });

      // Check for artifacts related to this execution
      const state = await stateStore.get();
      const artifacts = state ? Object.values(state.artifacts || {}).filter(
        (artifact: any) => artifact.executionId === executionId
      ) : [];

      // Check for reviews related to this execution
      const reviews = state ? Object.values(state.reviews).filter(
        (review: any) => review.executionId === executionId
      ) : [];

      return NextResponse.json({
        success: true,
        data: {
          execution: {
            id: execution.id,
            workflowId: execution.workflowId,
            status: execution.status,
            progress: execution.progress,
            currentStep: execution.currentStep,
            startedAt: execution.startedAt,
            completedAt: execution.completedAt,
            error: execution.error
          },
          workflow: workflow ? {
            id: workflow.id,
            name: workflow.name,
            stepCount: workflow.steps.length,
            steps: workflow.steps.map(s => ({
              id: s.id,
              name: s.name,
              type: s.type,
              hasReviewConfig: !!s.config.reviewConfig
            }))
          } : null,
          stepDetails,
          artifacts: artifacts.map((artifact: any) => ({
            id: artifact.id,
            stepId: artifact.stepId,
            type: artifact.type,
            status: artifact.status,
            title: artifact.title
          })),
          reviews: reviews.map((review: any) => ({
            id: review.id,
            stepId: review.stepId,
            type: review.type,
            status: review.status
          })),
          summary: {
            totalSteps: stepDetails.length,
            approvalGates: stepDetails.filter(s => s.stepType === 'approval_gate').length,
            humanReviews: stepDetails.filter(s => s.stepType === 'human_review').length,
            waitingApproval: stepDetails.filter(s => s.status === 'waiting_approval').length,
            waitingReview: stepDetails.filter(s => s.status === 'waiting_review').length,
            artifactCount: artifacts.length,
            reviewCount: reviews.length
          }
        }
      });
    }

    // Default: return system status
    const state = await stateStore.get();
    const executions = await stateStore.getAllExecutions();
    
    return NextResponse.json({
      success: true,
      data: {
        systemStatus: {
          totalExecutions: executions.length,
          runningExecutions: executions.filter(e => e.status === 'running').length,
          completedExecutions: executions.filter(e => e.status === 'completed').length,
          failedExecutions: executions.filter(e => e.status === 'failed').length,
          totalReviews: state ? Object.keys(state.reviews).length : 0,
          totalArtifacts: state ? Object.keys(state.artifacts || {}).length : 0,
          pendingReviews: state ? Object.values(state.reviews).filter((r: any) => r.status === 'pending').length : 0,
          pendingArtifacts: state ? Object.values(state.artifacts || {}).filter((a: any) => a.status === 'pending_approval').length : 0
        }
      }
    });

  } catch (error) {
    console.error('Debug API error:', error);
    return NextResponse.json(
      {
        error: 'Debug API failed',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
