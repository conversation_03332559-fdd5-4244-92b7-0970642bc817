/**
 * Feedback Analyzer
 * Analyzes rejection feedback to determine the best course of action
 */

export interface FeedbackAnalysisResult {
  isActionable: boolean;
  suggestedAction: 'regenerate' | 'skip_to_next' | 'manual_review';
  feedbackCategories: string[];
  regenerationStrategy?: 'content_only' | 'with_seo' | 'full_workflow';
  confidence: number;
  severity: 'minor' | 'moderate' | 'major';
  estimatedImprovementTime: number; // in seconds
  keyIssues: string[];
  improvementAreas: string[];
}

export class FeedbackAnalyzer {
  
  /**
   * Analyze rejection feedback to determine the best course of action
   */
  analyzeRejectionFeedback(feedback: string): FeedbackAnalysisResult {
    console.log(`🧠 Analyzing feedback: "${feedback.substring(0, 100)}..."`);
    
    // Clean and normalize feedback
    const normalizedFeedback = this.normalizeFeedback(feedback);
    
    // Categorize feedback
    const categories = this.categorizeFeedback(normalizedFeedback);
    
    // Determine actionability
    const isActionable = this.isActionableFeedback(normalizedFeedback, categories);
    
    // Assess severity
    const severity = this.assessSeverity(normalizedFeedback, categories);
    
    // Calculate confidence
    const confidence = this.calculateConfidence(normalizedFeedback, categories);
    
    // Determine suggested action
    const suggestedAction = this.determineSuggestedAction(isActionable, severity, categories);
    
    // Determine regeneration strategy
    const regenerationStrategy = this.determineRegenerationStrategy(categories, severity);
    
    // Extract key issues and improvement areas
    const keyIssues = this.extractKeyIssues(normalizedFeedback, categories);
    const improvementAreas = this.extractImprovementAreas(categories);
    
    // Estimate improvement time
    const estimatedImprovementTime = this.estimateImprovementTime(severity, categories.length);
    
    const result: FeedbackAnalysisResult = {
      isActionable,
      suggestedAction,
      feedbackCategories: categories,
      regenerationStrategy,
      confidence,
      severity,
      estimatedImprovementTime,
      keyIssues,
      improvementAreas
    };
    
    console.log(`📊 Feedback analysis result:`, {
      isActionable,
      suggestedAction,
      severity,
      confidence: Math.round(confidence * 100) + '%',
      categories: categories.length
    });
    
    return result;
  }
  
  /**
   * Normalize feedback text for analysis
   */
  private normalizeFeedback(feedback: string): string {
    return feedback
      .toLowerCase()
      .trim()
      .replace(/[^\w\s.,!?-]/g, ' ')
      .replace(/\s+/g, ' ');
  }
  
  /**
   * Categorize feedback into different types
   */
  private categorizeFeedback(feedback: string): string[] {
    const categories: string[] = [];
    
    // Content quality issues
    if (this.matchesPattern(feedback, ['basic', 'shallow', 'lacks depth', 'too simple', 'superficial'])) {
      categories.push('content_depth');
    }
    
    if (this.matchesPattern(feedback, ['technical', 'details', 'examples', 'specific', 'concrete'])) {
      categories.push('technical_detail');
    }
    
    if (this.matchesPattern(feedback, ['engaging', 'boring', 'interesting', 'captivating', 'compelling'])) {
      categories.push('engagement');
    }
    
    // Writing style issues
    if (this.matchesPattern(feedback, ['professional', 'tone', 'voice', 'style', 'formal', 'casual'])) {
      categories.push('writing_style');
    }
    
    // Structure issues
    if (this.matchesPattern(feedback, ['structure', 'organization', 'flow', 'order', 'sequence', 'layout'])) {
      categories.push('structure');
    }
    
    // SEO issues
    if (this.matchesPattern(feedback, ['seo', 'keywords', 'search', 'optimization', 'ranking', 'meta'])) {
      categories.push('seo');
    }
    
    // Accuracy issues
    if (this.matchesPattern(feedback, ['accurate', 'correct', 'wrong', 'error', 'mistake', 'fact'])) {
      categories.push('accuracy');
    }
    
    // Length issues
    if (this.matchesPattern(feedback, ['longer', 'shorter', 'length', 'brief', 'detailed', 'expand'])) {
      categories.push('length');
    }
    
    return categories;
  }
  
  /**
   * Check if feedback matches any of the given patterns
   */
  private matchesPattern(feedback: string, patterns: string[]): boolean {
    return patterns.some(pattern => feedback.includes(pattern));
  }
  
  /**
   * Determine if feedback is actionable
   */
  private isActionableFeedback(feedback: string, categories: string[]): boolean {
    // Feedback is actionable if:
    // 1. It's longer than 10 characters
    // 2. It contains specific categories
    // 3. It's not just generic negative feedback
    
    if (feedback.length < 10) return false;
    if (categories.length === 0) return false;
    
    // Check for generic negative feedback
    const genericNegative = ['bad', 'terrible', 'awful', 'hate', 'sucks', 'no good'];
    const isGeneric = genericNegative.some(word => feedback.includes(word)) && categories.length < 2;
    
    return !isGeneric;
  }
  
  /**
   * Assess the severity of the feedback
   */
  private assessSeverity(feedback: string, categories: string[]): 'minor' | 'moderate' | 'major' {
    // Severity indicators
    const majorIndicators = ['completely', 'totally', 'entirely', 'fundamental', 'major', 'serious'];
    const minorIndicators = ['slightly', 'minor', 'small', 'little', 'bit'];
    
    const hasMajorIndicators = majorIndicators.some(indicator => feedback.includes(indicator));
    const hasMinorIndicators = minorIndicators.some(indicator => feedback.includes(indicator));
    
    if (hasMajorIndicators || categories.length >= 4) return 'major';
    if (hasMinorIndicators || categories.length <= 1) return 'minor';
    return 'moderate';
  }
  
  /**
   * Calculate confidence in the analysis
   */
  private calculateConfidence(feedback: string, categories: string[]): number {
    let confidence = 0.5; // Base confidence
    
    // Increase confidence based on feedback length
    if (feedback.length > 50) confidence += 0.2;
    if (feedback.length > 100) confidence += 0.1;
    
    // Increase confidence based on number of categories
    confidence += Math.min(0.3, categories.length * 0.1);
    
    // Increase confidence if feedback is specific
    const specificWords = ['example', 'specific', 'detail', 'particular', 'exactly'];
    if (specificWords.some(word => feedback.includes(word))) {
      confidence += 0.1;
    }
    
    return Math.min(0.95, confidence);
  }
  
  /**
   * Determine the suggested action based on analysis
   */
  private determineSuggestedAction(
    isActionable: boolean, 
    severity: string, 
    categories: string[]
  ): 'regenerate' | 'skip_to_next' | 'manual_review' {
    if (!isActionable) return 'skip_to_next';
    
    if (severity === 'major' || categories.length >= 3) return 'regenerate';
    if (severity === 'moderate' && categories.length >= 2) return 'regenerate';
    if (categories.includes('accuracy')) return 'manual_review';
    
    return 'regenerate';
  }
  
  /**
   * Determine the regeneration strategy
   */
  private determineRegenerationStrategy(
    categories: string[], 
    severity: string
  ): 'content_only' | 'with_seo' | 'full_workflow' {
    if (categories.includes('seo') || severity === 'major') return 'with_seo';
    if (categories.length >= 3) return 'with_seo';
    return 'content_only';
  }
  
  /**
   * Extract key issues from feedback
   */
  private extractKeyIssues(feedback: string, categories: string[]): string[] {
    const issues: string[] = [];
    
    if (categories.includes('content_depth')) {
      issues.push('Content lacks sufficient depth and detail');
    }
    if (categories.includes('technical_detail')) {
      issues.push('Missing technical details and examples');
    }
    if (categories.includes('engagement')) {
      issues.push('Content is not engaging enough');
    }
    if (categories.includes('writing_style')) {
      issues.push('Writing style needs improvement');
    }
    if (categories.includes('structure')) {
      issues.push('Content structure and organization needs work');
    }
    if (categories.includes('seo')) {
      issues.push('SEO optimization is insufficient');
    }
    if (categories.includes('accuracy')) {
      issues.push('Content accuracy needs verification');
    }
    if (categories.includes('length')) {
      issues.push('Content length is not appropriate');
    }
    
    return issues;
  }
  
  /**
   * Extract improvement areas
   */
  private extractImprovementAreas(categories: string[]): string[] {
    const areas = categories.map(category => {
      switch (category) {
        case 'content_depth': return 'Content depth and comprehensiveness';
        case 'technical_detail': return 'Technical accuracy and detail';
        case 'engagement': return 'Reader engagement and interest';
        case 'writing_style': return 'Writing style and tone';
        case 'structure': return 'Content structure and flow';
        case 'seo': return 'SEO optimization';
        case 'accuracy': return 'Factual accuracy';
        case 'length': return 'Content length optimization';
        default: return category;
      }
    });
    
    return areas;
  }
  
  /**
   * Estimate time needed for improvement
   */
  private estimateImprovementTime(severity: string, categoryCount: number): number {
    let baseTime = 30; // 30 seconds base
    
    switch (severity) {
      case 'minor': baseTime = 20; break;
      case 'moderate': baseTime = 45; break;
      case 'major': baseTime = 90; break;
    }
    
    // Add time for each category
    baseTime += categoryCount * 15;
    
    return baseTime;
  }
}
